<script setup lang="ts">

</script>

<template>
  <div class="department">
    <div class="header">
      <div class="left">
        <svg t="1755842675724" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
          p-id="5125" width="16" height="16">
          <path
            d="M400.696008 129.093147c-17.248849 0-31.233352 13.984503-31.233352 31.233352l0 30.470989c0 17.248849 13.984503 31.233352 31.233352 31.233352s31.233352-13.984503 31.233352-31.233352l0-30.470989C431.92936 143.078673 417.944857 129.093147 400.696008 129.093147z"
            fill="#FF7F27" p-id="5126"></path>
          <path
            d="M623.647823 129.093147c-17.248849 0-31.233352 13.984503-31.233352 31.233352l0 30.470989c0 17.248849 13.985526 31.233352 31.233352 31.233352 17.248849 0 31.233352-13.984503 31.233352-31.233352l0-30.470989C654.881175 143.078673 640.896672 129.093147 623.647823 129.093147z"
            fill="#FF7F27" p-id="5127"></path>
          <path
            d="M425.695379 312.937269c11.209296 18.047028 41.974997 48.588625 86.152149 48.588625 43.958164 0 75.100442-30.308283 86.573751-48.223305 9.302877-14.528901 5.068436-33.846876-9.455349-43.149752-14.539134-9.307993-33.851992-5.063319-43.149752 9.455349-0.121773 0.198521-13.379729 19.449981-33.968649 19.449981-19.993357 0-32.428573-18.107403-33.271778-19.373233-9.17087-14.417361-28.28009-18.799158-42.829458-9.760295C421.089477 279.028994 416.591023 298.28557 425.695379 312.937269z"
            fill="#FF7F27" p-id="5128"></path>
          <path
            d="M564.242851 625.945145l-20.278859 0L543.963992 462.486306c0-17.253966-13.985526-31.233352-31.233352-31.233352-17.248849 0-31.233352 13.979386-31.233352 31.233352l0 163.457816-20.283975 0c-45.924959 0-83.289961 37.363979-83.289961 83.289961l0 103.024421c0 45.924959 37.363979 83.289961 83.289961 83.289961l103.029538 0c45.924959 0 83.289961-37.363979 83.289961-83.289961L647.532813 709.234083C647.532813 663.309124 610.168834 625.945145 564.242851 625.945145zM585.066109 812.258505c0 11.286044-9.537214 20.822235-20.822235 20.822235L461.214337 833.080739c-11.286044 0-20.822235-9.537214-20.822235-20.822235L440.392102 709.234083c0-11.286044 9.537214-20.822235 20.822235-20.822235l103.029538 0c11.286044 0 20.822235 9.537214 20.822235 20.822235L585.066109 812.258505z"
            fill="#FF7F27" p-id="5129"></path>
          <path
            d="M250.808256 625.945145l-17.482163 0 0-266.970354c0-35.483142 28.864398-64.35368 64.343447-64.35368 17.248849 0 31.233352-13.984503 31.233352-31.233352s-13.985526-31.233352-31.233352-31.233352c-69.924559 0-126.810151 56.890708-126.810151 126.820384l0 266.970354-23.079648 0c-45.924959 0-83.289961 37.363979-83.289961 83.289961l0 103.024421c0 45.924959 37.363979 83.289961 83.289961 83.289961l103.029538 0c45.924959 0 83.289961-37.363979 83.289961-83.289961L334.099241 709.234083C334.098217 663.309124 296.734238 625.945145 250.808256 625.945145zM271.630491 812.258505c0 11.286044-9.537214 20.822235-20.822235 20.822235L147.778718 833.080739c-11.286044 0-20.822235-9.537214-20.822235-20.822235L126.956484 709.234083c0-11.286044 9.537214-20.822235 20.822235-20.822235l103.029538 0c11.286044 0 20.822235 9.537214 20.822235 20.822235L271.630491 812.258505z"
            fill="#FF7F27" p-id="5130"></path>
          <path
            d="M876.565113 625.945145l-21.961174 0 0-266.970354c0-69.929676-56.890708-126.820384-126.815267-126.820384-17.248849 0-31.233352 13.985526-31.233352 31.233352s13.984503 31.233352 31.233352 31.233352c35.483142 0 64.348564 28.869514 64.348564 64.35368l0 266.970354-18.605753 0c-45.924959 0-83.289961 37.363979-83.289961 83.289961l0 103.024421c0 45.924959 37.363979 83.289961 83.289961 83.289961l103.034655 0c45.924959 0 83.289961-37.363979 83.289961-83.289961L959.856098 709.234083C959.854051 663.309124 922.490072 625.945145 876.565113 625.945145zM897.387347 812.258505c0 11.286044-9.537214 20.822235-20.822235 20.822235L773.530458 833.080739c-11.286044 0-20.822235-9.537214-20.822235-20.822235L752.708224 709.234083c0-11.286044 9.537214-20.822235 20.822235-20.822235l103.034655 0c11.286044 0 20.822235 9.537214 20.822235 20.822235L897.387347 812.258505z"
            fill="#FF7F27" p-id="5131"></path>
        </svg>
        <span>常见科室</span>
      </div>
      <div class="more">
        <a href="">全部</a>
        <svg t="1755842987645" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
          p-id="13954" width="16" height="16">
          <path
            d="M857.766234 511.488511L345.623017 0 297.174825 49.348412 759.846873 511.488511 297.174825 974.651588 345.623017 1022.977023z"
            fill="#8a8a8a" p-id="13955"></path>
        </svg>
      </div>
    </div>
    <div class="content">
      <ul>
        <li>神经内科</li>
        <li>消化内科</li>
        <li>呼吸内科</li>
        <li>内科</li>
        <li>神经外科</li>
        <li>妇科</li>
        <li>产科</li>
        <li>儿科</li>
      </ul>
    </div>
  </div>
  <div class="announcement">
    <div class="header">
      <div class="left">
        <svg t="1755844427647" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
          p-id="15444" width="16" height="16">
          <path
            d="M512 329.152a256 256 0 0 1 256 256v265.76a73.152 73.152 0 0 1 36.416 58.368l0.16 4.992H219.424c0-27.04 14.72-50.688 36.544-63.328L256 585.152a256 256 0 0 1 256-256z m-36.576 274.272c-28.704 0-52.256 22.048-54.656 50.144l-0.192 4.704v146.304h109.696V658.24l-0.224-5.28a54.848 54.848 0 0 0-54.624-49.568zM235.52 238.624l51.712 51.744A36.576 36.576 0 1 1 235.52 342.08L183.776 290.368a36.576 36.576 0 1 1 51.712-51.744z m553.024 0a36.576 36.576 0 0 1 51.712 51.744l-51.712 51.712a36.576 36.576 0 1 1-51.712-51.712zM512 109.76c20.192 0 36.576 16.352 36.576 36.544v73.152a36.576 36.576 0 1 1-73.152 0V146.272c0-20.16 16.384-36.544 36.576-36.544z"
            fill="#1296db" p-id="15445"></path>
        </svg> <span>平台公告</span>
      </div>
      <div class="more">
        <a href="">全部</a>
        <svg t="1755842987645" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
          p-id="13954" width="16" height="16">
          <path
            d="M857.766234 511.488511L345.623017 0 297.174825 49.348412 759.846873 511.488511 297.174825 974.651588 345.623017 1022.977023z"
            fill="#8a8a8a" p-id="13955"></path>
        </svg>
      </div>
    </div>
    <div class="content">
      <ul>
        <li>关于延长北京大学国际医院等等等等等等等等</li>
        <li>北京中医药大学东方医院部等等等等等等等等</li>
        <li>武警总医院号源暂停更新通知</li>
      </ul>
    </div>
  </div>
</template>

<style scoped lang="scss">
.department {
  font-size: 14px;
  font-weight: normal;
  color: gray;
  user-select: none;

  .header {
    display: flex;
    justify-content: space-between;

    .left {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .more {
      display: flex;
      align-items: center;

      a {
        text-decoration: none;
        color: gray;

        &:hover {
          color: #55a6fe;
        }

      }
    }
  }

  ul {
    margin: 30px 0 0 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    list-style-type: none;

    li {
      width: 50%;
      margin: 6px 0;
      cursor: pointer;

      &:hover {
        color: #55a6fe;
      }
    }
  }
}

.announcement {
  margin-top: 30px;
  font-size: 14px;
  font-weight: normal;
  color: gray;
  user-select: none;

  .header {
    display: flex;
    justify-content: space-between;

    .left {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .more {
      display: flex;
      align-items: center;

      a {
        text-decoration: none;
        color: gray;

        &:hover {
          color: #55a6fe;
        }

      }
    }
  }

  ul {
    margin: 30px 0 0 0;
    padding: 0;

    li {
      margin: 6px 0 0 0px;
      cursor: pointer;
      list-style-position: inside;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-left: 8px; /* 调整列表标记和文字之间的距离 */

      &:hover {
        color: #55a6fe;
      }
    }
  }
}
</style>
